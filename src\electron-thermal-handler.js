/**
 * Electron Main Process Handler for Thermal Printing
 * This file should be included in the main Electron process
 */

const { ipcMain, BrowserWindow } = require('electron');
const printer = require('printer');
const fs = require('fs');
const path = require('path');

class ElectronThermalHandler {
  constructor() {
    this.setupIpcHandlers();
  }

  setupIpcHandlers() {
    // Get available printers
    ipcMain.handle('get-printers', async () => {
      try {
        const printers = printer.getPrinters();
        return printers.map(p => ({
          name: p.name,
          displayName: p.displayName || p.name,
          description: p.description,
          status: p.status,
          isDefault: p.isDefault,
          options: p.options
        }));
      } catch (error) {
        console.error('Error getting printers:', error);
        return [];
      }
    });

    // Print to thermal printer
    ipcMain.handle('print-thermal', async (event, printData) => {
      try {
        const { printer: printerName, content, options } = printData;
        
        // If printer is 'auto', find the first available thermal printer
        let targetPrinter = printerName;
        if (printerName === 'auto') {
          const printers = printer.getPrinters();
          const thermalPrinter = printers.find(p => 
            p.name.toLowerCase().includes('thermal') ||
            p.name.toLowerCase().includes('receipt') ||
            p.name.toLowerCase().includes('pos') ||
            p.description?.toLowerCase().includes('thermal') ||
            p.description?.toLowerCase().includes('receipt')
          );
          
          if (thermalPrinter) {
            targetPrinter = thermalPrinter.name;
          } else {
            // Use default printer if no thermal printer found
            const defaultPrinter = printers.find(p => p.isDefault);
            targetPrinter = defaultPrinter ? defaultPrinter.name : printers[0]?.name;
          }
        }

        if (!targetPrinter) {
          throw new Error('No printer available');
        }

        // Method 1: Direct ESC/POS printing (for thermal printers)
        if (this.isThermalContent(content)) {
          return await this.printESCPOS(targetPrinter, content);
        }

        // Method 2: HTML printing via hidden window
        return await this.printHTML(targetPrinter, content, options);

      } catch (error) {
        console.error('Thermal print error:', error);
        throw error;
      }
    });

    // Print HTML content silently
    ipcMain.handle('print-html-silent', async (event, { html, printerName }) => {
      try {
        return await this.printHTMLSilent(html, printerName);
      } catch (error) {
        console.error('Silent HTML print error:', error);
        throw error;
      }
    });
  }

  /**
   * Check if content is ESC/POS commands
   */
  isThermalContent(content) {
    return typeof content === 'string' && content.includes('\x1B');
  }

  /**
   * Print ESC/POS content directly to thermal printer
   */
  async printESCPOS(printerName, escposContent) {
    return new Promise((resolve, reject) => {
      try {
        // Convert string to buffer
        const buffer = Buffer.from(escposContent, 'binary');
        
        // Print raw data
        printer.printDirect({
          data: buffer,
          printer: printerName,
          type: 'RAW',
          success: (jobID) => {
            console.log(`Thermal print job ${jobID} sent successfully`);
            resolve({ success: true, jobID, method: 'escpos' });
          },
          error: (error) => {
            console.error('ESC/POS print error:', error);
            reject(error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Print HTML content via hidden browser window
   */
  async printHTML(printerName, htmlContent, options = {}) {
    return new Promise((resolve, reject) => {
      // Create hidden window for printing
      const printWindow = new BrowserWindow({
        width: 400,
        height: 600,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      });

      // Load HTML content
      printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

      printWindow.webContents.once('did-finish-load', () => {
        // Print options
        const printOptions = {
          silent: true,
          printBackground: false,
          color: false,
          margin: {
            marginType: 'none'
          },
          landscape: false,
          scaleFactor: 100,
          ...options
        };

        // If specific printer is requested
        if (printerName && printerName !== 'auto') {
          printOptions.deviceName = printerName;
        }

        // Print
        printWindow.webContents.print(printOptions, (success, failureReason) => {
          printWindow.close();
          
          if (success) {
            resolve({ success: true, method: 'html' });
          } else {
            reject(new Error(`Print failed: ${failureReason}`));
          }
        });
      });

      printWindow.webContents.once('did-fail-load', (event, errorCode, errorDescription) => {
        printWindow.close();
        reject(new Error(`Failed to load content: ${errorDescription}`));
      });

      // Timeout after 10 seconds
      setTimeout(() => {
        if (!printWindow.isDestroyed()) {
          printWindow.close();
          reject(new Error('Print timeout'));
        }
      }, 10000);
    });
  }

  /**
   * Print HTML content silently without dialog
   */
  async printHTMLSilent(htmlContent, printerName) {
    const printWindow = new BrowserWindow({
      width: 400,
      height: 600,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    return new Promise((resolve, reject) => {
      printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

      printWindow.webContents.once('did-finish-load', () => {
        const printOptions = {
          silent: true,
          printBackground: false,
          color: false,
          margin: { marginType: 'none' },
          landscape: false,
          scaleFactor: 100
        };

        if (printerName && printerName !== 'auto') {
          printOptions.deviceName = printerName;
        }

        printWindow.webContents.print(printOptions, (success, failureReason) => {
          printWindow.close();
          
          if (success) {
            resolve({ success: true, method: 'html-silent' });
          } else {
            reject(new Error(`Silent print failed: ${failureReason}`));
          }
        });
      });

      printWindow.webContents.once('did-fail-load', (event, errorCode, errorDescription) => {
        printWindow.close();
        reject(new Error(`Failed to load content: ${errorDescription}`));
      });

      setTimeout(() => {
        if (!printWindow.isDestroyed()) {
          printWindow.close();
          reject(new Error('Silent print timeout'));
        }
      }, 10000);
    });
  }

  /**
   * Get thermal printer settings
   */
  getThermalPrinterSettings() {
    return {
      paperWidth: 80, // mm
      charactersPerLine: 48,
      supportedCommands: [
        'ESC/POS',
        'STAR',
        'CITIZEN'
      ]
    };
  }
}

// Initialize the handler
const thermalHandler = new ElectronThermalHandler();

module.exports = thermalHandler;
