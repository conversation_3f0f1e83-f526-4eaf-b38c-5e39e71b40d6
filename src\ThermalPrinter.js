/**
 * Thermal Printer Utility for Direct Printing
 * Handles direct communication with thermal printers using ESC/POS commands
 * Supports 80mm thermal printers with automatic print without dialog
 */

class ThermalPrinter {
  constructor() {
    this.isElectron = window.require !== undefined;
    this.printerName = localStorage.getItem('defaultThermalPrinter') || 'auto';
    this.encoding = 'utf8';
    this.paperWidth = 80; // 80mm paper
    this.charactersPerLine = 48; // Characters per line for 80mm
    
    // ESC/POS Commands
    this.commands = {
      INIT: '\x1B\x40',           // Initialize printer
      FEED_LINE: '\x0A',          // Line feed
      CUT_PAPER: '\x1D\x56\x00',  // Cut paper
      ALIGN_CENTER: '\x1B\x61\x01', // Center alignment
      ALIGN_LEFT: '\x1B\x61\x00',   // Left alignment
      ALIGN_RIGHT: '\x1B\x61\x02',  // Right alignment
      BOLD_ON: '\x1B\x45\x01',      // Bold on
      BOLD_OFF: '\x1B\x45\x00',     // Bold off
      DOUBLE_HEIGHT: '\x1B\x21\x10', // Double height
      DOUBLE_WIDTH: '\x1B\x21\x20',  // Double width
      DOUBLE_SIZE: '\x1B\x21\x30',   // Double size (width + height)
      NORMAL_SIZE: '\x1B\x21\x00',   // Normal size
      UNDERLINE_ON: '\x1B\x2D\x01',  // Underline on
      UNDERLINE_OFF: '\x1B\x2D\x00', // Underline off
    };
  }

  /**
   * Check if direct printing is available
   */
  isDirectPrintingAvailable() {
    return this.isElectron || (window.navigator && window.navigator.serial);
  }

  /**
   * Get available printers (Electron only)
   */
  async getAvailablePrinters() {
    if (!this.isElectron) {
      return [];
    }

    try {
      const { ipcRenderer } = window.require('electron');
      return await ipcRenderer.invoke('get-printers');
    } catch (error) {
      console.error('Error getting printers:', error);
      return [];
    }
  }

  /**
   * Set default thermal printer
   */
  setDefaultPrinter(printerName) {
    this.printerName = printerName;
    localStorage.setItem('defaultThermalPrinter', printerName);
  }

  /**
   * Format text for thermal printer
   */
  formatText(text, options = {}) {
    const {
      align = 'left',
      bold = false,
      doubleSize = false,
      underline = false
    } = options;

    let formatted = '';

    // Set alignment
    switch (align) {
      case 'center':
        formatted += this.commands.ALIGN_CENTER;
        break;
      case 'right':
        formatted += this.commands.ALIGN_RIGHT;
        break;
      default:
        formatted += this.commands.ALIGN_LEFT;
    }

    // Set text formatting
    if (bold) formatted += this.commands.BOLD_ON;
    if (doubleSize) formatted += this.commands.DOUBLE_SIZE;
    if (underline) formatted += this.commands.UNDERLINE_ON;

    // Add text
    formatted += text;

    // Reset formatting
    if (underline) formatted += this.commands.UNDERLINE_OFF;
    if (doubleSize) formatted += this.commands.NORMAL_SIZE;
    if (bold) formatted += this.commands.BOLD_OFF;

    // Add line feed
    formatted += this.commands.FEED_LINE;

    return formatted;
  }

  /**
   * Create separator line
   */
  createSeparator(char = '-', length = this.charactersPerLine) {
    return this.formatText(char.repeat(length), { align: 'center' });
  }

  /**
   * Create two-column text (label: value) - LTR
   */
  createTwoColumnText(label, value, totalWidth = this.charactersPerLine) {
    const labelLength = label.length;
    const valueLength = value.toString().length;
    const spacesNeeded = totalWidth - labelLength - valueLength;
    const spaces = spacesNeeded > 0 ? ' '.repeat(spacesNeeded) : ' ';

    return this.formatText(label + spaces + value);
  }

  /**
   * Create two-column text (value: label) - RTL
   */
  createTwoColumnTextRTL(label, value, totalWidth = this.charactersPerLine) {
    const labelLength = label.length;
    const valueLength = value.toString().length;
    const spacesNeeded = totalWidth - labelLength - valueLength;
    const spaces = spacesNeeded > 0 ? ' '.repeat(spacesNeeded) : ' ';

    // RTL: value first, then spaces, then label
    return this.formatText(value + spaces + label, { align: 'right' });
  }

  /**
   * Print directly to thermal printer (Electron)
   */
  async printDirectElectron(content) {
    if (!this.isElectron) {
      throw new Error('Electron not available');
    }

    try {
      const { ipcRenderer } = window.require('electron');
      
      // Prepare print data
      const printData = {
        printer: this.printerName,
        content: content,
        options: {
          silent: true,
          printBackground: false,
          color: false,
          margin: {
            marginType: 'none'
          },
          landscape: false,
          scaleFactor: 100
        }
      };

      return await ipcRenderer.invoke('print-thermal', printData);
    } catch (error) {
      console.error('Error printing to thermal printer:', error);
      throw error;
    }
  }

  /**
   * Print using Web Serial API (Chrome/Edge)
   */
  async printDirectSerial(content) {
    if (!window.navigator.serial) {
      throw new Error('Web Serial API not supported');
    }

    try {
      // Request serial port
      const port = await navigator.serial.requestPort();
      
      // Open the port
      await port.open({ 
        baudRate: 9600,
        dataBits: 8,
        stopBits: 1,
        parity: 'none'
      });

      // Create writer
      const writer = port.writable.getWriter();
      
      // Convert content to Uint8Array
      const encoder = new TextEncoder();
      const data = encoder.encode(content);
      
      // Write data
      await writer.write(data);
      
      // Close writer and port
      writer.releaseLock();
      await port.close();
      
      return true;
    } catch (error) {
      console.error('Error printing via Serial:', error);
      throw error;
    }
  }

  /**
   * Fallback to browser print with optimized settings
   */
  printFallback(htmlContent) {
    const printWindow = window.open('', '_blank', 'width=400,height=700');
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Auto print with minimal delay
    printWindow.onload = function() {
      // Try to print silently first
      try {
        printWindow.print();
        setTimeout(() => {
          printWindow.close();
        }, 1000);
      } catch (error) {
        console.error('Auto print failed:', error);
        // Manual print as fallback
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      }
    };
  }

  /**
   * Main print method - tries direct printing first, falls back to browser print
   */
  async print(content, htmlFallback = null) {
    try {
      // Try Electron direct printing first
      if (this.isElectron) {
        await this.printDirectElectron(content);
        return { success: true, method: 'electron' };
      }

      // Try Web Serial API
      if (window.navigator.serial) {
        await this.printDirectSerial(content);
        return { success: true, method: 'serial' };
      }

      // Fallback to browser print
      if (htmlFallback) {
        this.printFallback(htmlFallback);
        return { success: true, method: 'browser' };
      }

      throw new Error('No printing method available');

    } catch (error) {
      console.error('Print error:', error);
      
      // Final fallback to browser print if HTML content provided
      if (htmlFallback) {
        this.printFallback(htmlFallback);
        return { success: true, method: 'browser-fallback' };
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Generate ESC/POS content for invoice with RTL support
   */
  generateInvoiceESCPOS(invoice, storeSettings, formatPrice, t, currentLanguage = 'en') {
    let content = '';
    const isRTL = currentLanguage === 'ar';

    // Initialize printer
    content += this.commands.INIT;

    // Store header
    content += this.formatText(storeSettings.storeName, { 
      align: 'center', 
      bold: true, 
      doubleSize: true 
    });
    
    if (storeSettings.storePhone) {
      content += this.formatText(storeSettings.storePhone, { align: 'center', bold: true });
    }
    
    if (storeSettings.storeAddress) {
      content += this.formatText(storeSettings.storeAddress, { align: 'center' });
    }

    // Separator
    content += this.createSeparator('=');

    // Invoice info (RTL-aware)
    const invoiceLabel = t('invoiceNumberLabel', isRTL ? 'فاتورة رقم:' : 'Invoice #:');
    const dateLabel = t('dateLabel', isRTL ? 'التاريخ:' : 'Date:');
    const customerLabel = t('customerLabel', isRTL ? 'الزبون:' : 'Customer:');
    const walkInCustomer = t('walkInCustomer', isRTL ? 'زبون عابر' : 'Walk-in');

    if (isRTL) {
      // RTL layout - right to left
      content += this.createTwoColumnTextRTL(invoiceLabel, invoice.invoiceNumber);
      content += this.createTwoColumnTextRTL(dateLabel, invoice.date);
      content += this.createTwoColumnTextRTL(customerLabel, invoice.customerName || walkInCustomer);
    } else {
      // LTR layout - left to right
      content += this.createTwoColumnText(invoiceLabel, invoice.invoiceNumber);
      content += this.createTwoColumnText(dateLabel, invoice.date);
      content += this.createTwoColumnText(customerLabel, invoice.customerName || walkInCustomer);
    }

    // Separator
    content += this.createSeparator('-');

    // Items header
    const productsLabel = t('productsLabel', isRTL ? 'المنتجات' : 'PRODUCTS');
    content += this.formatText(productsLabel, {
      align: 'center',
      bold: true
    });
    content += this.createSeparator('-');

    // Items (RTL-aware)
    invoice.items.forEach(item => {
      content += this.formatText(item.productName, {
        bold: true,
        align: isRTL ? 'right' : 'left'
      });

      const quantityPrice = `${item.quantity} x ${formatPrice(item.price)}`;
      const total = formatPrice(item.total);

      if (isRTL) {
        content += this.createTwoColumnTextRTL(quantityPrice, total);
      } else {
        content += this.createTwoColumnText(quantityPrice, total);
      }
    });

    // Totals separator
    content += this.createSeparator('=');

    // Totals (RTL-aware)
    const subtotalLabel = t('subtotalLabel', isRTL ? 'المجموع الفرعي:' : 'Subtotal:');
    const discountLabel = t('discountLabel', isRTL ? 'الخصم:' : 'Discount:');
    const taxLabel = t('taxLabel', isRTL ? 'الضريبة:' : 'Tax:');
    const finalTotalLabel = t('finalTotalLabel', isRTL ? 'المجموع النهائي:' : 'TOTAL:');

    if (isRTL) {
      content += this.createTwoColumnTextRTL(subtotalLabel, formatPrice(invoice.total));

      if (invoice.discount > 0) {
        content += this.createTwoColumnTextRTL(discountLabel, `-${formatPrice(invoice.discount)}`);
      }

      content += this.createTwoColumnTextRTL(taxLabel, formatPrice(invoice.tax));
    } else {
      content += this.createTwoColumnText(subtotalLabel, formatPrice(invoice.total));

      if (invoice.discount > 0) {
        content += this.createTwoColumnText(discountLabel, `-${formatPrice(invoice.discount)}`);
      }

      content += this.createTwoColumnText(taxLabel, formatPrice(invoice.tax));
    }

    // Final total
    content += this.createSeparator('=');
    content += this.formatText(
      `${finalTotalLabel} ${formatPrice(invoice.finalTotal)}`,
      { align: 'center', bold: true, doubleSize: true }
    );
    content += this.createSeparator('=');

    // Footer (RTL-aware)
    content += this.commands.FEED_LINE;
    const thankYouMessage = t('thankYouMessage', isRTL ? 'شكراً لزيارتكم' : 'Thank you!');
    content += this.formatText(thankYouMessage, {
      align: 'center',
      bold: true
    });

    // Developer footer
    content += this.commands.FEED_LINE;
    const developerText = isRTL ? 'تطوير iCode DZ' : 'Developed by iCode DZ';
    content += this.formatText(developerText, {
      align: 'center',
      bold: true
    });
    content += this.formatText('0551930589', {
      align: 'center',
      bold: true
    });

    // Cut paper
    content += this.commands.FEED_LINE;
    content += this.commands.FEED_LINE;
    content += this.commands.CUT_PAPER;

    return content;
  }

  /**
   * Generate ESC/POS content for customer data with RTL support
   */
  generateCustomerESCPOS(customer, storeSettings, t, currentLanguage = 'en') {
    let content = '';
    const isRTL = currentLanguage === 'ar';

    // Initialize printer
    content += this.commands.INIT;

    // Store header
    content += this.formatText(storeSettings.storeName, {
      align: 'center',
      bold: true,
      doubleSize: true
    });

    // Report title
    const reportTitle = t('customerReport', isRTL ? 'تقرير الزبون' : 'Customer Report');
    content += this.formatText(reportTitle, {
      align: 'center',
      bold: true
    });

    // Date
    const dateStr = new Date().toLocaleDateString(isRTL ? 'ar-DZ' : 'en-US');
    content += this.formatText(dateStr, {
      align: 'center'
    });

    // Separator
    content += this.createSeparator('=');

    // Customer info (RTL-aware)
    const customerNumberLabel = t('customerNumber', isRTL ? 'رقم الزبون:' : 'Customer #:');
    const customerNameLabel = t('customerName', isRTL ? 'اسم الزبون:' : 'Name:');
    const phoneLabel = t('phone', isRTL ? 'الهاتف:' : 'Phone:');
    const emailLabel = t('email', isRTL ? 'البريد الإلكتروني:' : 'Email:');
    const companyLabel = t('company', isRTL ? 'الشركة:' : 'Company:');

    if (isRTL) {
      content += this.createTwoColumnTextRTL(customerNumberLabel, customer.id);
      content += this.createTwoColumnTextRTL(customerNameLabel, customer.name);
      content += this.createTwoColumnTextRTL(phoneLabel, customer.phone || '-');
      content += this.createTwoColumnTextRTL(emailLabel, customer.email || '-');
      content += this.createTwoColumnTextRTL(companyLabel, customer.company || '-');
    } else {
      content += this.createTwoColumnText(customerNumberLabel, customer.id);
      content += this.createTwoColumnText(customerNameLabel, customer.name);
      content += this.createTwoColumnText(phoneLabel, customer.phone || '-');
      content += this.createTwoColumnText(emailLabel, customer.email || '-');
      content += this.createTwoColumnText(companyLabel, customer.company || '-');
    }

    // Balance (highlighted)
    content += this.createSeparator('=');
    const balanceLabel = t('balance', isRTL ? 'الرصيد:' : 'Balance:');
    const balanceText = `${balanceLabel} ${customer.balance.toLocaleString()}`;
    content += this.formatText(balanceText, {
      align: 'center',
      bold: true,
      doubleSize: true
    });
    content += this.createSeparator('=');

    // Additional info (RTL-aware)
    const discountLabel = t('discountPercentage', isRTL ? 'خصم هامش الربح:' : 'Discount:');
    const statusLabel = t('status', isRTL ? 'الحالة:' : 'Status:');
    const activeText = t('active', isRTL ? 'نشط' : 'Active');
    const inactiveText = t('inactive', isRTL ? 'غير نشط' : 'Inactive');

    const statusText = customer.status === 'نشط' ? activeText : inactiveText;

    if (isRTL) {
      content += this.createTwoColumnTextRTL(discountLabel, `${customer.discountPercentage || 0}%`);
      content += this.createTwoColumnTextRTL(statusLabel, statusText);
    } else {
      content += this.createTwoColumnText(discountLabel, `${customer.discountPercentage || 0}%`);
      content += this.createTwoColumnText(statusLabel, statusText);
    }

    // Footer
    content += this.commands.FEED_LINE;
    const developerText = isRTL ? 'تطوير iCode DZ' : 'Developed by iCode DZ';
    content += this.formatText(developerText, {
      align: 'center',
      bold: true
    });
    content += this.formatText('0551930589', {
      align: 'center',
      bold: true
    });

    // Cut paper
    content += this.commands.FEED_LINE;
    content += this.commands.FEED_LINE;
    content += this.commands.CUT_PAPER;

    return content;
  }
}

// Create singleton instance
const thermalPrinter = new ThermalPrinter();

export default thermalPrinter;
