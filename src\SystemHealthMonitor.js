/**
 * System Health Monitor for iCode DZ POS System
 * Prevents system blocking and provides automatic recovery
 */

class SystemHealthMonitor {
  constructor() {
    this.isMonitoring = false;
    this.healthCheckInterval = null;
    this.performanceMetrics = {
      memoryUsage: 0,
      responseTime: 0,
      errorCount: 0,
      lastActivity: Date.now()
    };
    this.thresholds = {
      maxMemoryUsage: 100 * 1024 * 1024, // 100MB
      maxResponseTime: 5000, // 5 seconds
      maxErrorCount: 10,
      inactivityTimeout: 30 * 60 * 1000 // 30 minutes
    };
    this.recoveryActions = [];
    this.isRecovering = false;
  }

  /**
   * Start system health monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    console.log('🔍 System Health Monitor: Started monitoring');

    // Run health check every 30 seconds
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000);

    // Monitor user activity
    this.setupActivityMonitoring();

    // Monitor memory usage
    this.setupMemoryMonitoring();

    // Monitor errors
    this.setupErrorMonitoring();
  }

  /**
   * Stop system health monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    console.log('🔍 System Health Monitor: Stopped monitoring');
  }

  /**
   * Perform comprehensive health check
   */
  performHealthCheck() {
    const healthStatus = {
      timestamp: Date.now(),
      memory: this.checkMemoryUsage(),
      performance: this.checkPerformance(),
      errors: this.checkErrorCount(),
      activity: this.checkUserActivity(),
      overall: 'healthy'
    };

    // Determine overall health
    if (healthStatus.memory.status === 'critical' || 
        healthStatus.performance.status === 'critical' ||
        healthStatus.errors.status === 'critical') {
      healthStatus.overall = 'critical';
      this.triggerRecovery('critical', healthStatus);
    } else if (healthStatus.memory.status === 'warning' || 
               healthStatus.performance.status === 'warning' ||
               healthStatus.errors.status === 'warning') {
      healthStatus.overall = 'warning';
      this.triggerRecovery('warning', healthStatus);
    }

    // Log health status
    if (healthStatus.overall !== 'healthy') {
      console.warn('⚠️ System Health Monitor: Health check results:', healthStatus);
    }

    return healthStatus;
  }

  /**
   * Check memory usage
   */
  checkMemoryUsage() {
    let memoryUsage = 0;
    let status = 'healthy';

    try {
      if (performance.memory) {
        memoryUsage = performance.memory.usedJSHeapSize;
        
        if (memoryUsage > this.thresholds.maxMemoryUsage * 0.9) {
          status = 'critical';
        } else if (memoryUsage > this.thresholds.maxMemoryUsage * 0.7) {
          status = 'warning';
        }
      }
    } catch (error) {
      console.error('🔍 System Health Monitor: Memory check failed:', error);
    }

    this.performanceMetrics.memoryUsage = memoryUsage;

    return {
      status,
      usage: memoryUsage,
      threshold: this.thresholds.maxMemoryUsage,
      percentage: (memoryUsage / this.thresholds.maxMemoryUsage) * 100
    };
  }

  /**
   * Check system performance
   */
  checkPerformance() {
    const now = Date.now();
    const responseTime = now - this.performanceMetrics.lastActivity;
    let status = 'healthy';

    if (responseTime > this.thresholds.maxResponseTime * 2) {
      status = 'critical';
    } else if (responseTime > this.thresholds.maxResponseTime) {
      status = 'warning';
    }

    this.performanceMetrics.responseTime = responseTime;

    return {
      status,
      responseTime,
      threshold: this.thresholds.maxResponseTime
    };
  }

  /**
   * Check error count
   */
  checkErrorCount() {
    let status = 'healthy';

    if (this.performanceMetrics.errorCount > this.thresholds.maxErrorCount * 2) {
      status = 'critical';
    } else if (this.performanceMetrics.errorCount > this.thresholds.maxErrorCount) {
      status = 'warning';
    }

    return {
      status,
      count: this.performanceMetrics.errorCount,
      threshold: this.thresholds.maxErrorCount
    };
  }

  /**
   * Check user activity
   */
  checkUserActivity() {
    const now = Date.now();
    const inactiveTime = now - this.performanceMetrics.lastActivity;
    let status = 'healthy';

    if (inactiveTime > this.thresholds.inactivityTimeout) {
      status = 'inactive';
    }

    return {
      status,
      inactiveTime,
      threshold: this.thresholds.inactivityTimeout
    };
  }

  /**
   * Setup activity monitoring
   */
  setupActivityMonitoring() {
    const updateActivity = () => {
      this.performanceMetrics.lastActivity = Date.now();
    };

    // Monitor various user interactions
    document.addEventListener('click', updateActivity, true);
    document.addEventListener('keydown', updateActivity, true);
    document.addEventListener('scroll', updateActivity, true);
    document.addEventListener('mousemove', updateActivity, true);
  }

  /**
   * Setup memory monitoring
   */
  setupMemoryMonitoring() {
    // Monitor for memory leaks
    setInterval(() => {
      if (performance.memory && performance.memory.usedJSHeapSize > this.thresholds.maxMemoryUsage) {
        console.warn('⚠️ System Health Monitor: High memory usage detected');
        this.triggerMemoryCleanup();
      }
    }, 60000); // Check every minute
  }

  /**
   * Setup error monitoring
   */
  setupErrorMonitoring() {
    // Monitor global errors
    window.addEventListener('error', (event) => {
      this.performanceMetrics.errorCount++;
      console.error('🔍 System Health Monitor: Error detected:', event.error);
    });

    // Monitor unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.performanceMetrics.errorCount++;
      console.error('🔍 System Health Monitor: Unhandled promise rejection:', event.reason);
    });
  }

  /**
   * Trigger recovery actions
   */
  triggerRecovery(severity, healthStatus) {
    if (this.isRecovering) return;

    this.isRecovering = true;
    console.warn(`🔧 System Health Monitor: Triggering ${severity} recovery`);

    try {
      switch (severity) {
        case 'warning':
          this.performLightRecovery(healthStatus);
          break;
        case 'critical':
          this.performFullRecovery(healthStatus);
          break;
      }
    } catch (error) {
      console.error('🔧 System Health Monitor: Recovery failed:', error);
    } finally {
      this.isRecovering = false;
    }
  }

  /**
   * Perform light recovery actions
   */
  performLightRecovery(healthStatus) {
    console.log('🔧 System Health Monitor: Performing light recovery');

    // Clear unnecessary data
    this.clearTemporaryData();

    // Reset error count
    if (healthStatus.errors.status === 'warning') {
      this.performanceMetrics.errorCount = 0;
    }

    // Trigger garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  /**
   * Perform full recovery actions
   */
  performFullRecovery(healthStatus) {
    console.log('🔧 System Health Monitor: Performing full recovery');

    // Clear all temporary data
    this.clearTemporaryData();

    // Reset all metrics
    this.performanceMetrics.errorCount = 0;
    this.performanceMetrics.lastActivity = Date.now();

    // Force garbage collection
    if (window.gc) {
      window.gc();
    }

    // Clear browser caches if possible
    this.clearBrowserCaches();

    // Show recovery notification
    this.showRecoveryNotification();
  }

  /**
   * Clear temporary data
   */
  clearTemporaryData() {
    try {
      // Clear any temporary variables or caches
      // This can be customized based on your application's needs
      
      // Clear console if too many logs
      if (console.clear && this.performanceMetrics.errorCount > 50) {
        console.clear();
      }
    } catch (error) {
      console.error('🔧 System Health Monitor: Failed to clear temporary data:', error);
    }
  }

  /**
   * Clear browser caches
   */
  clearBrowserCaches() {
    try {
      // Clear session storage of non-essential items
      const keysToKeep = [
        'icaldz-login-status',
        'icaldz-current-page',
        'icaldz-settings',
        'icaldz-products',
        'icaldz-customers',
        'icaldz-invoices'
      ];

      Object.keys(sessionStorage).forEach(key => {
        if (!keysToKeep.includes(key)) {
          sessionStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('🔧 System Health Monitor: Failed to clear caches:', error);
    }
  }

  /**
   * Trigger memory cleanup
   */
  triggerMemoryCleanup() {
    console.log('🔧 System Health Monitor: Triggering memory cleanup');
    
    // Force garbage collection
    if (window.gc) {
      window.gc();
    }

    // Clear temporary data
    this.clearTemporaryData();
  }

  /**
   * Show recovery notification
   */
  showRecoveryNotification() {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 15px;
      border-radius: 5px;
      z-index: 10000;
      font-family: Arial, sans-serif;
      font-size: 14px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    `;
    notification.textContent = '🔧 System optimized automatically';
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  /**
   * Get current health status
   */
  getHealthStatus() {
    return this.performHealthCheck();
  }

  /**
   * Reset all metrics
   */
  resetMetrics() {
    this.performanceMetrics = {
      memoryUsage: 0,
      responseTime: 0,
      errorCount: 0,
      lastActivity: Date.now()
    };
  }
}

// Create singleton instance
const systemHealthMonitor = new SystemHealthMonitor();

export default systemHealthMonitor;
