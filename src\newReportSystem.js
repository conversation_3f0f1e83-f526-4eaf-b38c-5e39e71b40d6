/**
 * COMPLETELY NEW REPORT SYSTEM - CLEAN FROM SCRATCH
 * This replaces ALL old calculation code
 */

import { generateCleanDailyReport, validateDailyReport, calculateCostOfGoodsSold } from './profitCalculator.js';
import { ALGERIAN_MONTHS, getAlgerianMonth } from './algerianLocale.js';
import { getTranslation } from './translations.js';

/**
 * Generate Daily Report UI with CLEAN calculations
 */
export function generateNewDailyReportUI(savedInvoices, products, expenses, formatPrice, showToast, currentLanguage = 'ar') {
  console.log('🚀 NEW CLEAN DAILY REPORT SYSTEM STARTING...');

  // Translation helper
  const t = (key, fallback) => getTranslation(key, currentLanguage) || fallback;

  // Get language settings
  const isRTL = currentLanguage === 'ar';
  const langCode = currentLanguage === 'ar' ? 'ar' : currentLanguage === 'fr' ? 'fr' : 'en';

  // Validate input data
  if (!Array.isArray(savedInvoices) || !Array.isArray(products) || !Array.isArray(expenses)) {
    showToast(t('invalidDataReload', '❌ Invalid data, please reload the page'), 'error');
    return;
  }

  // Generate report using the CLEAN calculation system
  const report = generateCleanDailyReport(savedInvoices, products, expenses, new Date());

  console.log('📊 CLEAN REPORT GENERATED:', report);

  // Validate the report calculations
  const isValid = validateDailyReport(report);
  if (!isValid) {
    showToast(t('reportValidationFailed', '⚠️ Report calculation validation failed'), 'warning');
  }

  // Check for errors and warnings
  if (report.hasErrors) {
    showToast(t('missingProductsWarning', `⚠️ Warning: ${report.costDetails.missingProducts.length} missing products`), 'warning', 5000);
  }

  if (report.warnings.length > 0) {
    console.warn('Report warnings:', report.warnings);
  }

  // Generate the CLEAN HTML report
  const reportContent = `
    <!DOCTYPE html>
    <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${langCode}">
    <head>
      <meta charset="UTF-8">
      <title>${t('dailyReport', 'التقرير اليومي')} - ${t('accountingSystem', 'نظام المحاسبي')}</title>
      <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${isRTL ? 'rtl' : 'ltr'}; margin: 20px; background: #f5f5f5; }
        .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }
        .header p { color: #7f8c8d; margin: 5px 0; }
        .calculation-verification { background: #e8f5e8; border: 2px solid #27ae60; border-radius: 10px; padding: 20px; margin: 20px 0; }
        .calculation-verification h3 { color: #27ae60; margin: 0 0 15px 0; }
        .calculation-step { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; }
        .daily-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }
        .stat-card .value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .stat-card.sales .value { color: #3498db; }
        .stat-card.cogs .value { color: #e74c3c; }
        .stat-card.gross-profit .value { color: #f39c12; }
        .stat-card.expenses .value { color: #9b59b6; }
        .stat-card.net-profit .value { color: #27ae60; }
        .stat-card.net-profit.negative .value { color: #e74c3c; }
        .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }
        @media print { body { margin: 0; background: white; } .header, .stat-card { box-shadow: none; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>📊 ${t('dailyReport', 'التقرير اليومي')} - ${t('newSystem', 'نظام جديد')}</h1>
        <p>${t('reportDate', 'تاريخ التقرير')}: ${new Date(report.date).toLocaleDateString(langCode === 'ar' ? 'ar-DZ' : langCode === 'fr' ? 'fr-FR' : 'en-US')}</p>
        <p>${t('accountingSystem', 'نظام المحاسبي')} - ${t('accurateCalculations', 'حسابات دقيقة 100%')}</p>
      </div>

      <div class="daily-stats">
        <div class="stat-card sales">
          <h3>${t('totalSales', 'إجمالي المبيعات')}</h3>
          <div class="value">${formatPrice(report.totalSales)}</div>
          <small>${report.invoiceCount} ${t('invoice', 'فاتورة')}</small>
        </div>
        <div class="stat-card cogs">
          <h3>${t('costOfGoodsSold', 'تكلفة البضاعة المباعة')}</h3>
          <div class="value">${formatPrice(report.costOfGoodsSold)}</div>
          <small>${report.processedItems} ${t('item', 'عنصر')}</small>
        </div>
        <div class="stat-card gross-profit">
          <h3>${t('grossProfit', 'الربح الإجمالي')}</h3>
          <div class="value">${formatPrice(report.grossProfit)}</div>
          <small>${t('beforeExpenses', 'قبل المصاريف')}</small>
        </div>
        <div class="stat-card expenses">
          <h3>${t('operatingExpenses', 'المصاريف التشغيلية')}</h3>
          <div class="value">${formatPrice(report.dailyExpenses)}</div>
          <small>${report.expenseCount} ${t('expense', 'مصروف')}</small>
        </div>
        <div class="stat-card net-profit ${report.netProfit < 0 ? 'negative' : ''}">
          <h3>${t('dailyNetProfit', 'صافي الربح اليومي')}</h3>
          <div class="value">${formatPrice(report.netProfit)}</div>
          <small>${report.netProfit >= 0 ? t('profit', 'ربح') : t('loss', 'خسارة')}</small>
        </div>
        <div class="stat-card">
          <h3>${t('profitMargin', 'هامش الربح')}</h3>
          <div class="value">${report.profitMargin}%</div>
          <small>${t('fromSales', 'من المبيعات')}</small>
        </div>
      </div>

      <div class="footer">
        <p>${t('reportGeneratedBy', 'تم إنشاء هذا التقرير بواسطة النظام الجديد المحسن - حسابات دقيقة 100%')}</p>
        <p><strong>Developed by iCode DZ</strong></p>
        <p><strong>📞 0551930589</strong></p>
        <p>${t('allRightsReserved', '© 2025 iCode DZ - جميع الحقوق محفوظة')}</p>
        <p><small>${t('processedItems', 'تم معالجة')} ${report.processedItems} ${t('item', 'عنصر')} ${t('from', 'من')} ${report.invoiceCount} ${t('invoice', 'فاتورة')}</small></p>
      </div>
    </body>
    </html>
  `;

  // Open the report in a new window
  const printWindow = window.open('', '_blank', 'width=1200,height=800');
  printWindow.document.write(reportContent);
  printWindow.document.close();
  printWindow.onload = () => setTimeout(() => printWindow.print(), 1000);

  // Success message
  const successMessage = report.hasErrors || report.warnings.length > 0
    ? t('dailyReportOpenedWithWarnings', '📊 تم فتح التقرير اليومي الجديد مع تحذيرات - يرجى مراجعة البيانات')
    : t('dailyReportOpenedSuccessfully', '📊 تم فتح التقرير اليومي الجديد بنجاح - حسابات دقيقة 100%');

  showToast(successMessage, report.hasErrors ? 'warning' : 'success', 3000);
}

/**
 * Generate Annual Report with CLEAN calculations
 */
export function generateNewAnnualReport(savedInvoices, products, expenses, formatPrice, showToast, currentLanguage = 'ar') {
  console.log('🚀 NEW CLEAN ANNUAL REPORT SYSTEM STARTING...');

  // Translation helper
  const t = (key, fallback) => getTranslation(key, currentLanguage) || fallback;

  // Get language settings
  const isRTL = currentLanguage === 'ar';
  const langCode = currentLanguage === 'ar' ? 'ar' : currentLanguage === 'fr' ? 'fr' : 'en';

  const currentYear = new Date().getFullYear();

  // Filter annual data
  const annualInvoices = savedInvoices.filter(inv => {
    const invDate = new Date(inv.date);
    return invDate.getFullYear() === currentYear;
  });

  const annualExpenses = expenses.filter(exp => {
    const expDate = new Date(exp.date);
    return expDate.getFullYear() === currentYear;
  });

  // Calculate annual totals using CLEAN system
  const totalSales = annualInvoices.reduce((sum, inv) => sum + (parseFloat(inv.finalTotal) || 0), 0);
  const costCalculation = calculateCostOfGoodsSold(annualInvoices, products);
  const costOfGoodsSold = costCalculation.totalCost;
  const totalExpenses = annualExpenses.reduce((sum, exp) => sum + (parseFloat(exp.amount) || 0), 0);

  const grossProfit = totalSales - costOfGoodsSold;
  const netProfit = grossProfit - totalExpenses;
  const profitMargin = totalSales > 0 ? (netProfit / totalSales) * 100 : 0;

  // Monthly breakdown
  const monthlyData = {};
  for (let month = 0; month < 12; month++) {
    const monthInvoices = annualInvoices.filter(inv => {
      const invDate = new Date(inv.date);
      return invDate.getMonth() === month;
    });

    const monthExpenses = annualExpenses.filter(exp => {
      const expDate = new Date(exp.date);
      return expDate.getMonth() === month;
    });

    const monthSales = monthInvoices.reduce((sum, inv) => sum + (parseFloat(inv.finalTotal) || 0), 0);
    const monthCostCalc = calculateCostOfGoodsSold(monthInvoices, products);
    const monthCosts = monthCostCalc.totalCost;
    const monthExpensesTotal = monthExpenses.reduce((sum, exp) => sum + (parseFloat(exp.amount) || 0), 0);

    monthlyData[month] = {
      sales: monthSales,
      costs: monthCosts,
      expenses: monthExpensesTotal,
      grossProfit: monthSales - monthCosts,
      netProfit: (monthSales - monthCosts) - monthExpensesTotal,
      invoiceCount: monthInvoices.length
    };
  }

  const reportContent = `
    <!DOCTYPE html>
    <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${langCode}">
    <head>
      <meta charset="UTF-8">
      <title>${t('annualReport', 'التقرير السنوي')} - ${t('accountingSystem', 'نظام المحاسبي')}</title>
      <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${isRTL ? 'rtl' : 'ltr'}; margin: 20px; background: #f5f5f5; }
        .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }
        .annual-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #34495e; }
        .summary-card .value { font-size: 28px; font-weight: bold; margin-bottom: 5px; }
        .summary-card.sales .value { color: #3498db; }
        .summary-card.profit .value { color: #27ae60; }
        .summary-card.profit.negative .value { color: #e74c3c; }
        .monthly-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; direction: ${isRTL ? 'rtl' : 'ltr'}; }
        th { background: #34495e; color: white; padding: 15px; text-align: center; font-weight: bold; }
        td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }
        tr:nth-child(even) { background: #f8f9fa; }
        .positive { color: #27ae60; font-weight: bold; }
        .negative { color: #e74c3c; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }
      </style>
    </head>
    <body class="lang-${currentLanguage}">
      <div class="header">
        <h1>📊 ${t('annualReport', 'التقرير السنوي')} ${currentYear}</h1>
        <p>${t('annualOperationsSummary', 'ملخص العمليات السنوية')} - ${t('improvedNewSystem', 'نظام جديد محسن')}</p>
        <p>${t('accurateCalculations', 'حسابات دقيقة 100%')} - ${t('noErrors', 'بدون أخطاء')}</p>
      </div>

      <div class="annual-summary">
        <div class="summary-card sales">
          <h3>${t('totalSales', 'إجمالي المبيعات')}</h3>
          <div class="value">${formatPrice(totalSales)}</div>
          <small>${annualInvoices.length} ${t('invoice', 'فاتورة')}</small>
        </div>
        <div class="summary-card">
          <h3>${t('costOfGoodsSold', 'تكلفة البضاعة المباعة')}</h3>
          <div class="value" style="color: #e74c3c;">${formatPrice(costOfGoodsSold)}</div>
          <small>${costCalculation.processedItems} ${t('item', 'عنصر')}</small>
        </div>
        <div class="summary-card">
          <h3>${t('totalExpenses', 'إجمالي المصاريف')}</h3>
          <div class="value" style="color: #9b59b6;">${formatPrice(totalExpenses)}</div>
          <small>${annualExpenses.length} ${t('expense', 'مصروف')}</small>
        </div>
        <div class="summary-card profit ${netProfit < 0 ? 'negative' : ''}">
          <h3>${t('annualNetProfit', 'صافي الربح السنوي')}</h3>
          <div class="value">${formatPrice(netProfit)}</div>
          <small>${t('margin', 'هامش')} ${profitMargin.toFixed(1)}%</small>
        </div>
      </div>

      <div class="monthly-table">
        <h3 style="padding: 20px; margin: 0; background: #34495e; color: white;">📅 ${t('monthlyPerformance', 'الأداء الشهري')}</h3>
        <table>
          <thead>
            <tr>
              <th>${t('month', 'الشهر')}</th>
              <th>${t('sales', 'المبيعات')}</th>
              <th>${t('cost', 'التكلفة')}</th>
              <th>${t('expenses', 'المصاريف')}</th>
              <th>${t('netProfit', 'صافي الربح')}</th>
              <th>${t('invoiceCount', 'عدد الفواتير')}</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(monthlyData).map(([month, data]) => {
              const monthName = t(`month${parseInt(month) + 1}`, ALGERIAN_MONTHS[parseInt(month)]);
              return `
                <tr>
                  <td>${monthName}</td>
                  <td>${formatPrice(data.sales)}</td>
                  <td>${formatPrice(data.costs)}</td>
                  <td>${formatPrice(data.expenses)}</td>
                  <td class="${data.netProfit >= 0 ? 'positive' : 'negative'}">${formatPrice(data.netProfit)}</td>
                  <td>${data.invoiceCount}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>

      <div class="footer">
        <p>${t('reportGeneratedBy', 'تم إنشاء هذا التقرير بواسطة النظام الجديد المحسن - حسابات دقيقة 100%')}</p>
        <p><strong>Developed by iCode DZ</strong></p>
        <p><strong>📞 0551930589</strong></p>
        <p>${t('allRightsReserved', '© 2025 iCode DZ - جميع الحقوق محفوظة')}</p>
      </div>
    </body>
    </html>
  `;

  // Open the report in a new window
  const printWindow = window.open('', '_blank', 'width=1200,height=800');
  printWindow.document.write(reportContent);
  printWindow.document.close();
  printWindow.onload = () => setTimeout(() => printWindow.print(), 1000);

  showToast(t('annualReportOpenedSuccessfully', '📊 تم فتح التقرير السنوي الجديد بنجاح - حسابات دقيقة 100%'), 'success', 3000);
}

/**
 * Generate Monthly Report with CLEAN calculations
 */
export function generateNewMonthlyReport(savedInvoices, products, expenses, formatPrice, showToast, currentLanguage = 'ar') {
  console.log('🚀 NEW CLEAN MONTHLY REPORT SYSTEM STARTING...');

  // Translation helper
  const t = (key, fallback) => getTranslation(key, currentLanguage) || fallback;

  // Get language settings
  const isRTL = currentLanguage === 'ar';
  const langCode = currentLanguage === 'ar' ? 'ar' : currentLanguage === 'fr' ? 'fr' : 'en';

  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Filter monthly data
  const monthlyInvoices = savedInvoices.filter(inv => {
    const invDate = new Date(inv.date);
    return invDate.getMonth() === currentMonth && invDate.getFullYear() === currentYear;
  });

  const monthlyExpenses = expenses.filter(exp => {
    const expDate = new Date(exp.date);
    return expDate.getMonth() === currentMonth && expDate.getFullYear() === currentYear;
  });

  // Calculate monthly totals using CLEAN system
  const totalSales = monthlyInvoices.reduce((sum, inv) => sum + (parseFloat(inv.finalTotal) || 0), 0);
  const costCalculation = calculateCostOfGoodsSold(monthlyInvoices, products);
  const costOfGoodsSold = costCalculation.totalCost;
  const totalExpenses = monthlyExpenses.reduce((sum, exp) => sum + (parseFloat(exp.amount) || 0), 0);

  const grossProfit = totalSales - costOfGoodsSold;
  const netProfit = grossProfit - totalExpenses;
  const profitMargin = totalSales > 0 ? (netProfit / totalSales) * 100 : 0;

  // Daily breakdown for the month
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  const dailyData = {};

  for (let day = 1; day <= daysInMonth; day++) {
    const dateKey = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

    const dayInvoices = monthlyInvoices.filter(inv => inv.date === dateKey);
    const dayExpenses = monthlyExpenses.filter(exp => exp.date === dateKey);

    const daySales = dayInvoices.reduce((sum, inv) => sum + (parseFloat(inv.finalTotal) || 0), 0);
    const dayCostCalc = calculateCostOfGoodsSold(dayInvoices, products);
    const dayCosts = dayCostCalc.totalCost;
    const dayExpensesTotal = dayExpenses.reduce((sum, exp) => sum + (parseFloat(exp.amount) || 0), 0);

    dailyData[dateKey] = {
      sales: daySales,
      costs: dayCosts,
      expenses: dayExpensesTotal,
      grossProfit: daySales - dayCosts,
      netProfit: (daySales - dayCosts) - dayExpensesTotal,
      invoiceCount: dayInvoices.length
    };
  }

  const currentMonthName = t(`month${currentMonth + 1}`, getAlgerianMonth(currentMonth));

  const reportContent = `
    <!DOCTYPE html>
    <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${langCode}">
    <head>
      <meta charset="UTF-8">
      <title>${t('monthlyReport', 'التقرير الشهري')} - ${t('accountingSystem', 'نظام المحاسبي')}</title>
      <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${isRTL ? 'rtl' : 'ltr'}; margin: 20px; background: #f5f5f5; }
        .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }
        .monthly-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #34495e; }
        .summary-card .value { font-size: 28px; font-weight: bold; margin-bottom: 5px; }
        .summary-card.sales .value { color: #3498db; }
        .summary-card.profit .value { color: #27ae60; }
        .summary-card.profit.negative .value { color: #e74c3c; }
        .daily-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; direction: ${isRTL ? 'rtl' : 'ltr'}; }
        th { background: #34495e; color: white; padding: 15px; text-align: center; font-weight: bold; }
        td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }
        tr:nth-child(even) { background: #f8f9fa; }
        .positive { color: #27ae60; font-weight: bold; }
        .negative { color: #e74c3c; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }
      </style>
    </head>
    <body class="lang-${currentLanguage}">
      <div class="header">
        <h1>📅 ${t('monthlyReport', 'التقرير الشهري')} ${currentMonthName} ${currentYear}</h1>
        <p>${t('monthlyOperationsSummary', 'ملخص العمليات الشهرية')} - ${t('improvedNewSystem', 'نظام جديد محسن')}</p>
        <p>${t('accurateCalculations', 'حسابات دقيقة 100%')} - ${t('noErrors', 'بدون أخطاء')}</p>
      </div>

      <div class="monthly-summary">
        <div class="summary-card sales">
          <h3>${t('totalSales', 'إجمالي المبيعات')}</h3>
          <div class="value">${formatPrice(totalSales)}</div>
          <small>${monthlyInvoices.length} ${t('invoice', 'فاتورة')}</small>
        </div>
        <div class="summary-card">
          <h3>${t('costOfGoodsSold', 'تكلفة البضاعة المباعة')}</h3>
          <div class="value" style="color: #e74c3c;">${formatPrice(costOfGoodsSold)}</div>
          <small>${costCalculation.processedItems} ${t('item', 'عنصر')}</small>
        </div>
        <div class="summary-card">
          <h3>${t('totalExpenses', 'إجمالي المصاريف')}</h3>
          <div class="value" style="color: #9b59b6;">${formatPrice(totalExpenses)}</div>
          <small>${monthlyExpenses.length} ${t('expense', 'مصروف')}</small>
        </div>
        <div class="summary-card profit ${netProfit < 0 ? 'negative' : ''}">
          <h3>${t('monthlyNetProfit', 'صافي الربح الشهري')}</h3>
          <div class="value">${formatPrice(netProfit)}</div>
          <small>${t('margin', 'هامش')} ${profitMargin.toFixed(1)}%</small>
        </div>
      </div>

      <div class="daily-table">
        <h3 style="padding: 20px; margin: 0; background: #34495e; color: white;">📊 ${t('dailyPerformanceForMonth', 'الأداء اليومي للشهر')}</h3>
        <table>
          <thead>
            <tr>
              <th>${t('date', 'التاريخ')}</th>
              <th>${t('sales', 'المبيعات')}</th>
              <th>${t('cost', 'التكلفة')}</th>
              <th>${t('expenses', 'المصاريف')}</th>
              <th>${t('netProfit', 'صافي الربح')}</th>
              <th>${t('invoiceCount', 'عدد الفواتير')}</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(dailyData).map(([date, data]) => {
              const dayNumber = new Date(date).getDate();
              return `
                <tr>
                  <td>${dayNumber} ${currentMonthName}</td>
                  <td>${formatPrice(data.sales)}</td>
                  <td>${formatPrice(data.costs)}</td>
                  <td>${formatPrice(data.expenses)}</td>
                  <td class="${data.netProfit >= 0 ? 'positive' : 'negative'}">${formatPrice(data.netProfit)}</td>
                  <td>${data.invoiceCount}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>

      <div class="footer">
        <p>${t('reportGeneratedBy', 'تم إنشاء هذا التقرير بواسطة النظام الجديد المحسن - حسابات دقيقة 100%')}</p>
        <p><strong>Developed by iCode DZ</strong></p>
        <p><strong>📞 0551930589</strong></p>
        <p>${t('allRightsReserved', '© 2025 iCode DZ - جميع الحقوق محفوظة')}</p>
      </div>
    </body>
    </html>
  `;

  // Open the report in a new window
  const printWindow = window.open('', '_blank', 'width=1200,height=800');
  printWindow.document.write(reportContent);
  printWindow.document.close();
  printWindow.onload = () => setTimeout(() => printWindow.print(), 1000);

  showToast(t('monthlyReportOpenedSuccessfully', '📅 تم فتح التقرير الشهري الجديد بنجاح - حسابات دقيقة 100%'), 'success', 3000);
}
