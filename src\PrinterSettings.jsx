import React, { useState, useEffect } from 'react';
import ThermalPrinter from './ThermalPrinter.js';
import { getTranslation } from './translations.js';

const PrinterSettings = ({ currentLanguage, showToast, onClose }) => {
  const [availablePrinters, setAvailablePrinters] = useState([]);
  const [selectedPrinter, setSelectedPrinter] = useState('auto');
  const [isLoading, setIsLoading] = useState(false);
  const [directPrintingAvailable, setDirectPrintingAvailable] = useState(false);

  const t = (key, fallback) => getTranslation(key, currentLanguage) || fallback;

  useEffect(() => {
    checkDirectPrintingSupport();
    loadPrinterSettings();
    if (ThermalPrinter.isDirectPrintingAvailable()) {
      loadAvailablePrinters();
    }
  }, []);

  const checkDirectPrintingSupport = () => {
    const isAvailable = ThermalPrinter.isDirectPrintingAvailable();
    setDirectPrintingAvailable(isAvailable);
    
    if (!isAvailable) {
      showToast(
        t('directPrintingNotSupported', '⚠️ الطباعة المباشرة غير مدعومة - سيتم استخدام طباعة المتصفح'),
        'warning',
        5000
      );
    }
  };

  const loadPrinterSettings = () => {
    const savedPrinter = localStorage.getItem('defaultThermalPrinter') || 'auto';
    setSelectedPrinter(savedPrinter);
  };

  const loadAvailablePrinters = async () => {
    setIsLoading(true);
    try {
      const printers = await ThermalPrinter.getAvailablePrinters();
      setAvailablePrinters(printers);
      
      if (printers.length === 0) {
        showToast(
          t('noPrintersFound', '⚠️ لم يتم العثور على طابعات'),
          'warning',
          3000
        );
      }
    } catch (error) {
      console.error('Error loading printers:', error);
      showToast(
        t('errorLoadingPrinters', '❌ خطأ في تحميل الطابعات'),
        'error',
        3000
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrinterChange = (printerName) => {
    setSelectedPrinter(printerName);
    ThermalPrinter.setDefaultPrinter(printerName);
    showToast(
      t('printerSettingsSaved', '✅ تم حفظ إعدادات الطابعة'),
      'success',
      2000
    );
  };

  const testPrint = async () => {
    setIsLoading(true);
    try {
      const testContent = ThermalPrinter.formatText('=== TEST PRINT ===', { 
        align: 'center', 
        bold: true, 
        doubleSize: true 
      }) +
      ThermalPrinter.formatText('iCode DZ POS System', { align: 'center', bold: true }) +
      ThermalPrinter.formatText('Thermal Printer Test', { align: 'center' }) +
      ThermalPrinter.formatText(new Date().toLocaleString(), { align: 'center' }) +
      ThermalPrinter.createSeparator('=') +
      ThermalPrinter.formatText('If you can read this,', { align: 'center' }) +
      ThermalPrinter.formatText('your printer is working!', { align: 'center', bold: true }) +
      ThermalPrinter.createSeparator('=') +
      ThermalPrinter.formatText('Developed by iCode DZ', { align: 'center', bold: true }) +
      ThermalPrinter.formatText('0551930589', { align: 'center', bold: true }) +
      ThermalPrinter.commands.FEED_LINE +
      ThermalPrinter.commands.FEED_LINE +
      ThermalPrinter.commands.CUT_PAPER;

      const testHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <style>
            @page { size: 80mm auto; margin: 0; }
            body { 
              font-family: 'Courier New', monospace; 
              font-size: 14px; 
              font-weight: bold; 
              text-align: center; 
              padding: 3mm; 
              width: 74mm; 
            }
            .test-header { font-size: 18px; margin-bottom: 5mm; }
            .separator { border-bottom: 2px solid black; margin: 3mm 0; }
          </style>
        </head>
        <body>
          <div class="test-header">=== TEST PRINT ===</div>
          <div><strong>iCode DZ POS System</strong></div>
          <div>Thermal Printer Test</div>
          <div>${new Date().toLocaleString()}</div>
          <div class="separator"></div>
          <div>If you can read this,</div>
          <div><strong>your printer is working!</strong></div>
          <div class="separator"></div>
          <div><strong>Developed by iCode DZ</strong></div>
          <div><strong>0551930589</strong></div>
        </body>
        </html>
      `;

      const result = await ThermalPrinter.print(testContent, testHTML);
      
      if (result.success) {
        showToast(
          t('testPrintSent', '🖨️ تم إرسال طباعة تجريبية'),
          'success',
          3000
        );
      } else {
        showToast(
          t('testPrintFailed', '❌ فشل في الطباعة التجريبية'),
          'error',
          3000
        );
      }
    } catch (error) {
      console.error('Test print error:', error);
      showToast(
        t('testPrintError', '❌ خطأ في الطباعة التجريبية'),
        'error',
        3000
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content" style={{ maxWidth: '600px' }}>
        <div className="modal-header">
          <h2>🖨️ {t('printerSettings', 'إعدادات الطابعة')}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          {/* Direct Printing Status */}
          <div className="setting-group">
            <h3>{t('directPrintingStatus', 'حالة الطباعة المباشرة')}</h3>
            <div className={`status-indicator ${directPrintingAvailable ? 'available' : 'unavailable'}`}>
              {directPrintingAvailable ? (
                <>
                  <span className="status-icon">✅</span>
                  <span>{t('directPrintingAvailable', 'الطباعة المباشرة متاحة')}</span>
                </>
              ) : (
                <>
                  <span className="status-icon">⚠️</span>
                  <span>{t('directPrintingUnavailable', 'الطباعة المباشرة غير متاحة - سيتم استخدام طباعة المتصفح')}</span>
                </>
              )}
            </div>
          </div>

          {/* Printer Selection */}
          <div className="setting-group">
            <h3>{t('selectPrinter', 'اختيار الطابعة')}</h3>
            
            {isLoading ? (
              <div className="loading-indicator">
                <span>⏳ {t('loadingPrinters', 'جاري تحميل الطابعات...')}</span>
              </div>
            ) : (
              <div className="printer-selection">
                <div className="radio-group">
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="printer"
                      value="auto"
                      checked={selectedPrinter === 'auto'}
                      onChange={(e) => handlePrinterChange(e.target.value)}
                    />
                    <span>{t('autoPrinter', 'اختيار تلقائي (أول طابعة حرارية متاحة)')}</span>
                  </label>

                  {availablePrinters.map((printer) => (
                    <label key={printer.name} className="radio-option">
                      <input
                        type="radio"
                        name="printer"
                        value={printer.name}
                        checked={selectedPrinter === printer.name}
                        onChange={(e) => handlePrinterChange(e.target.value)}
                      />
                      <span>
                        {printer.displayName || printer.name}
                        {printer.isDefault && <span className="default-badge"> (افتراضي)</span>}
                        {printer.description && <small> - {printer.description}</small>}
                      </span>
                    </label>
                  ))}
                </div>

                {directPrintingAvailable && (
                  <button 
                    className="btn btn-secondary"
                    onClick={loadAvailablePrinters}
                    disabled={isLoading}
                  >
                    🔄 {t('refreshPrinters', 'تحديث قائمة الطابعات')}
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Test Print */}
          <div className="setting-group">
            <h3>{t('testPrint', 'طباعة تجريبية')}</h3>
            <p>{t('testPrintDescription', 'اطبع صفحة تجريبية للتأكد من عمل الطابعة بشكل صحيح')}</p>
            <button 
              className="btn btn-primary"
              onClick={testPrint}
              disabled={isLoading}
            >
              🖨️ {t('printTestPage', 'طباعة صفحة تجريبية')}
            </button>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-primary" onClick={onClose}>
            ✅ {t('close', 'إغلاق')}
          </button>
        </div>
      </div>

      <style jsx>{`
        .setting-group {
          margin-bottom: 25px;
          padding: 15px;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          background: #f8f9fa;
        }

        .setting-group h3 {
          margin: 0 0 15px 0;
          color: #333;
          font-size: 16px;
        }

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 10px;
          border-radius: 6px;
          font-weight: bold;
        }

        .status-indicator.available {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }

        .status-indicator.unavailable {
          background: #fff3cd;
          color: #856404;
          border: 1px solid #ffeaa7;
        }

        .radio-group {
          margin-bottom: 15px;
        }

        .radio-option {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 10px;
          margin-bottom: 8px;
          border: 1px solid #ddd;
          border-radius: 6px;
          background: white;
          cursor: pointer;
          transition: all 0.2s;
        }

        .radio-option:hover {
          background: #f0f0f0;
          border-color: #007bff;
        }

        .radio-option input[type="radio"] {
          margin: 0;
        }

        .default-badge {
          background: #007bff;
          color: white;
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 11px;
        }

        .loading-indicator {
          text-align: center;
          padding: 20px;
          color: #666;
        }
      `}</style>
    </div>
  );
};

export default PrinterSettings;
