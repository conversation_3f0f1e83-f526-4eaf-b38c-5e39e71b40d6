@echo off
title Enhanced Secure Activation Code Generator v2.0

:MAIN_MENU
cls
echo.
echo ================================================================
echo          Enhanced Secure Code Generator v2.0
echo                Multiple Security Layers
echo ================================================================
echo.
echo SECURITY FEATURES BEYOND MACHINE ID:
echo ----------------------------------------------------------------
echo.
echo 1. Hardware Fingerprinting (CPU, GPU, Audio, Battery)
echo 2. Network-Based Validation (IP restrictions, Server validation)
echo 3. Behavioral Analysis (Keystroke patterns, Mouse movements)
echo 4. Time-Based Security (TOTP, Session heartbeat)
echo 5. Geolocation Restrictions (Country/Region based)
echo 6. Anti-Tampering Detection (DevTools, Code integrity)
echo 7. Multi-Device Management (Device limits, Hardware binding)
echo 8. Continuous Monitoring (Real-time validation)
echo.
echo ----------------------------------------------------------------
echo.
echo GENERATION OPTIONS:
echo.
echo [1] Basic Security Code (Machine ID + Basic validation)
echo [2] Enhanced Security Code (Multiple layers)
echo [3] Maximum Security Code (All security features)
echo [4] Trial Code (Time-limited with security)
echo [5] View Generated Codes Database
echo [6] Validate Existing Code
echo [7] Security Tools and Utilities
echo [8] Help and Documentation
echo [9] Exit
echo.
set /p choice="Select option (1-9): "

if "%choice%"=="1" goto BASIC_CODE
if "%choice%"=="2" goto ENHANCED_CODE
if "%choice%"=="3" goto MAXIMUM_CODE
if "%choice%"=="4" goto TRIAL_CODE
if "%choice%"=="5" goto VIEW_CODES
if "%choice%"=="6" goto VALIDATE_CODE
if "%choice%"=="7" goto SECURITY_TOOLS
if "%choice%"=="8" goto HELP
if "%choice%"=="9" goto EXIT

echo Invalid choice. Please try again.
pause
goto MAIN_MENU

:BASIC_CODE
cls
echo.
echo ================================================================
echo                Basic Security Code Generator
echo ================================================================
echo.
echo Security Features:
echo - Machine ID binding
echo - Basic encryption (AES-256)
echo - One-time use validation
echo - Format verification
echo.
set /p client_name="Enter client name (or press Enter for default): "
if "%client_name%"=="" set client_name=Licensed User

set /p machine_id="Enter machine ID (optional): "

echo.
echo Generating basic security activation code...
node generate-enhanced-codes-cli.js --type=basic --client="%client_name%" --machine="%machine_id%"
echo.
pause
goto MAIN_MENU

:ENHANCED_CODE
cls
echo.
echo ================================================================
echo              Enhanced Security Code Generator
echo ================================================================
echo.
echo Security Features:
echo - Hardware fingerprinting (CPU, GPU, Audio)
echo - Network validation
echo - IP address restrictions
echo - Multi-device management
echo - Server-side validation
echo - Anti-tampering detection
echo.
set /p client_name="Enter client name: "
if "%client_name%"=="" set client_name=Licensed User

set /p machine_id="Enter machine ID (recommended): "

set /p allowed_ips="Enter allowed IP addresses (comma-separated, optional): "

set /p max_devices="Maximum allowed devices (default: 1): "
if "%max_devices%"=="" set max_devices=1

echo.
echo Generating enhanced security activation code...
echo Client: %client_name%
echo Machine ID: %machine_id%
echo Allowed IPs: %allowed_ips%
echo Max Devices: %max_devices%
echo.

node -e "
const EnhancedGenerator = require('./generate-enhanced-activation-code');
const generator = new EnhancedGenerator();
const result = generator.generateEnhancedActivationCode({
  clientName: '%client_name%',
  machineId: '%machine_id%' || null,
  securityLevel: 'ENHANCED',
  type: 'LIFETIME',
  allowedIPs: '%allowed_ips%' ? '%allowed_ips%'.split(',').map(ip => ip.trim()) : [],
  maxDevices: parseInt('%max_devices%') || 1,
  hardwareBinding: true,
  networkValidation: true
});

if (result) {
  console.log('\\n✅ Enhanced Activation Code Generated Successfully!');
  console.log('━'.repeat(60));
  console.log('🔑 Activation Code: ' + result.activationCode);
  console.log('👤 Client: ' + result.clientName);
  console.log('🆔 Client ID: ' + result.clientId);
  console.log('🔒 Security Level: ' + result.securityLevel);
  console.log('📅 Generated: ' + result.generatedAt);
  console.log('\\n🛡️ Security Features:');
  console.log('   • Machine Binding: ' + (result.securityFeatures.machineBinding ? '✅' : '❌'));
  console.log('   • IP Restriction: ' + (result.securityFeatures.ipRestriction ? '✅' : '❌'));
  console.log('   • Hardware Binding: ' + (result.securityFeatures.hardwareBinding ? '✅' : '❌'));
  console.log('   • Network Validation: ' + (result.securityFeatures.networkValidation ? '✅' : '❌'));
  console.log('   • Max Devices: ' + result.securityFeatures.maxDevices);
  console.log('\\n📋 Server Token: ' + result.serverToken);
  console.log('\\n⚠️ IMPORTANT: Store this code securely - it can only be used once!');
} else {
  console.log('❌ Failed to generate activation code');
}
"

echo.
pause
goto MAIN_MENU

:MAXIMUM_CODE
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🔐 Maximum Security Code Generator              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 ALL Security Features:
echo • Hardware fingerprinting (CPU, GPU, Audio, Battery, WebGL)
echo • Behavioral analysis (Keystroke patterns, Mouse tracking)
echo • Network validation with server verification
echo • IP address and geolocation restrictions
echo • Anti-tampering and debugging detection
echo • Continuous monitoring and heartbeat
echo • Multi-layer encryption (AES-256 + HMAC)
echo • Time-based validation windows
echo.
set /p client_name="👤 Enter client name: "
if "%client_name%"=="" set client_name=Licensed User

set /p machine_id="💻 Enter machine ID (REQUIRED): "
if "%machine_id%"=="" (
    echo ❌ Machine ID is required for maximum security!
    pause
    goto MAIN_MENU
)

set /p allowed_ips="🌐 Enter allowed IP addresses (comma-separated): "

set /p max_devices="📱 Maximum allowed devices (default: 1): "
if "%max_devices%"=="" set max_devices=1

set /p geo_restriction="🌍 Geographic restriction (country code, optional): "

echo.
echo 🔄 Generating MAXIMUM security activation code...
echo 📊 Client: %client_name%
echo 💻 Machine ID: %machine_id%
echo 🌐 Allowed IPs: %allowed_ips%
echo 📱 Max Devices: %max_devices%
echo 🌍 Geo Restriction: %geo_restriction%
echo.

node -e "
const EnhancedGenerator = require('./generate-enhanced-activation-code');
const generator = new EnhancedGenerator();
const result = generator.generateEnhancedActivationCode({
  clientName: '%client_name%',
  machineId: '%machine_id%',
  securityLevel: 'MAXIMUM',
  type: 'LIFETIME',
  allowedIPs: '%allowed_ips%' ? '%allowed_ips%'.split(',').map(ip => ip.trim()) : [],
  maxDevices: parseInt('%max_devices%') || 1,
  geoRestriction: '%geo_restriction%' || null,
  hardwareBinding: true,
  behaviorTracking: true,
  networkValidation: true,
  continuousMonitoring: true
});

if (result) {
  console.log('\\n🔐 MAXIMUM Security Activation Code Generated!');
  console.log('━'.repeat(60));
  console.log('🔑 Activation Code: ' + result.activationCode);
  console.log('👤 Client: ' + result.clientName);
  console.log('🆔 Client ID: ' + result.clientId);
  console.log('🔒 Security Level: MAXIMUM (' + result.securityLevel + ')');
  console.log('📅 Generated: ' + result.generatedAt);
  console.log('\\n🛡️ ALL Security Features ENABLED:');
  console.log('   • Machine Binding: ✅');
  console.log('   • IP Restriction: ' + (result.securityFeatures.ipRestriction ? '✅' : '❌'));
  console.log('   • Geo Restriction: ' + (result.securityFeatures.geoRestriction ? '✅' : '❌'));
  console.log('   • Hardware Binding: ✅');
  console.log('   • Behavior Tracking: ✅');
  console.log('   • Network Validation: ✅');
  console.log('   • Continuous Monitoring: ✅');
  console.log('   • Max Devices: ' + result.securityFeatures.maxDevices);
  console.log('\\n📋 Server Token: ' + result.serverToken);
  console.log('\\n🔐 This is the MOST SECURE activation code possible!');
  console.log('⚠️ CRITICAL: Store securely - cannot be regenerated!');
} else {
  console.log('❌ Failed to generate maximum security code');
}
"

echo.
pause
goto MAIN_MENU

:TRIAL_CODE
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🧪 Trial Code Generator                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ⏰ Trial Code Options:
echo • Time-limited activation (1, 7, 30 days)
echo • Configurable security level
echo • Automatic expiration
echo • One-time use only
echo.
set /p client_name="👤 Enter client name: "
if "%client_name%"=="" set client_name=Trial User

echo.
echo 📅 Trial Duration Options:
echo [1] 1 Day (Testing)
echo [2] 7 Days (Standard Trial)
echo [3] 30 Days (Extended Trial)
echo.
set /p trial_choice="Select trial duration (1-3): "

if "%trial_choice%"=="1" set trial_days=1
if "%trial_choice%"=="2" set trial_days=7
if "%trial_choice%"=="3" set trial_days=30
if "%trial_days%"=="" set trial_days=7

echo.
echo 🔒 Security Level Options:
echo [1] BASIC (Machine ID only)
echo [2] ENHANCED (Multiple layers)
echo [3] MAXIMUM (All features)
echo.
set /p security_choice="Select security level (1-3): "

if "%security_choice%"=="1" set security_level=BASIC
if "%security_choice%"=="2" set security_level=ENHANCED
if "%security_choice%"=="3" set security_level=MAXIMUM
if "%security_level%"=="" set security_level=ENHANCED

echo.
echo 🔄 Generating %trial_days%-day trial code with %security_level% security...

node -e "
const EnhancedGenerator = require('./generate-enhanced-activation-code');
const generator = new EnhancedGenerator();
const result = generator.generateEnhancedActivationCode({
  clientName: '%client_name%',
  securityLevel: '%security_level%',
  type: 'TRIAL',
  trialDays: %trial_days%,
  hardwareBinding: '%security_level%' !== 'BASIC',
  networkValidation: '%security_level%' !== 'BASIC',
  behaviorTracking: '%security_level%' === 'MAXIMUM',
  continuousMonitoring: '%security_level%' === 'MAXIMUM'
});

if (result) {
  console.log('\\n🧪 Trial Activation Code Generated Successfully!');
  console.log('━'.repeat(60));
  console.log('🔑 Activation Code: ' + result.activationCode);
  console.log('👤 Client: ' + result.clientName);
  console.log('📅 Trial Duration: %trial_days% days');
  console.log('⏰ Expires: ' + result.expiryDate);
  console.log('🔒 Security Level: %security_level%');
  console.log('\\n⚠️ TRIAL CODE LIMITATIONS:');
  console.log('• Valid for %trial_days% days only');
  console.log('• One-time use activation');
  console.log('• Cannot be extended or renewed');
  console.log('• Automatic expiration');
} else {
  console.log('❌ Failed to generate trial code');
}
"

echo.
pause
goto MAIN_MENU

:VIEW_CODES
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              📊 Generated Codes Database                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔄 Loading codes database...
node -e "
const fs = require('fs');
const path = require('path');

try {
  const dbPath = path.join(__dirname, 'used-codes-enhanced.json');

  if (!fs.existsSync(dbPath)) {
    console.log('📭 No codes database found. Generate some codes first!');
    return;
  }

  const codes = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const codeList = Object.keys(codes);

  if (codeList.length === 0) {
    console.log('📭 Database is empty. No codes generated yet.');
    return;
  }

  console.log('📈 Total codes in database: ' + codeList.length);
  console.log('━'.repeat(60));

  codeList.slice(0, 10).forEach((code, index) => {
    const data = codes[code];
    console.log((index + 1) + '. ' + code);
    console.log('   👤 Client: ' + data.clientName);
    console.log('   📊 Type: ' + data.type + ' | Security Level: ' + data.securityLevel);
    console.log('   📅 Generated: ' + data.generatedAt);
    console.log('   🔄 Status: ' + (data.used ? '❌ Used (' + data.usedAt + ')' : '✅ Available'));
    if (data.machineId) console.log('   💻 Machine ID: ' + data.machineId);
    if (data.allowedIPs && data.allowedIPs.length > 0) console.log('   🌐 Allowed IPs: ' + data.allowedIPs.join(', '));
    console.log('');
  });

  if (codeList.length > 10) {
    console.log('... and ' + (codeList.length - 10) + ' more codes');
  }

} catch (error) {
  console.log('❌ Error reading database: ' + error.message);
}
"
echo.
pause
goto MAIN_MENU

:VALIDATE_CODE
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                ✅ Code Validation Tool                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
set /p code_to_validate="🔑 Enter activation code to validate: "
set /p machine_id_validate="💻 Enter machine ID (optional): "

echo.
echo 🔄 Validating activation code...

node -e "
const EnhancedGenerator = require('./generate-enhanced-activation-code');
const generator = new EnhancedGenerator();

const validationData = {
  machineId: '%machine_id_validate%' || null,
  ipAddress: '127.0.0.1',
  timestamp: Date.now()
};

const result = generator.validateEnhancedActivationCode('%code_to_validate%', validationData);

if (result.valid) {
  console.log('\\n✅ ACTIVATION CODE IS VALID');
  console.log('━'.repeat(40));
  console.log('👤 Client: ' + result.data.clientName);
  console.log('📊 Type: ' + result.data.type);
  console.log('🔒 Security Level: ' + result.data.securityLevel);
  console.log('📅 Generated: ' + result.data.generatedAt);
  console.log('🔄 Status: ' + (result.data.used ? '❌ Already Used' : '✅ Available'));

  if (result.data.expiryDate) {
    const expiry = new Date(result.data.expiryDate);
    const now = new Date();
    const isExpired = now > expiry;
    console.log('⏰ Expires: ' + result.data.expiryDate + (isExpired ? ' (EXPIRED)' : ' (Valid)'));
  }

  if (result.data.machineId) {
    console.log('💻 Bound to Machine: ' + result.data.machineId);
  }

  if (result.data.allowedIPs && result.data.allowedIPs.length > 0) {
    console.log('🌐 Allowed IPs: ' + result.data.allowedIPs.join(', '));
  }

} else {
  console.log('\\n❌ ACTIVATION CODE IS INVALID');
  console.log('━'.repeat(40));
  console.log('🚫 Reason: ' + result.error);
}
"

echo.
pause
goto MAIN_MENU

:SECURITY_TOOLS
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🔧 Security Tools & Utilities                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🛠️ Available Tools:
echo.
echo [1] 🔍 Generate Machine Fingerprint
echo [2] 🌐 Check IP Address
echo [3] 🧹 Clean Database
echo [4] 📊 Security Statistics
echo [5] 🔙 Back to Main Menu
echo.
set /p tool_choice="Select tool (1-5): "

if "%tool_choice%"=="1" goto MACHINE_FINGERPRINT
if "%tool_choice%"=="2" goto CHECK_IP
if "%tool_choice%"=="3" goto CLEAN_DATABASE
if "%tool_choice%"=="4" goto SECURITY_STATS
if "%tool_choice%"=="5" goto MAIN_MENU

echo ❌ Invalid choice.
pause
goto SECURITY_TOOLS

:MACHINE_FINGERPRINT
echo.
echo 🔍 Generating machine fingerprint...
echo This would show hardware information in a real implementation.
echo Machine ID: %COMPUTERNAME%-%USERNAME%-%RANDOM%
echo.
pause
goto SECURITY_TOOLS

:CHECK_IP
echo.
echo 🌐 Checking IP address...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do echo Current IP: %%a
echo.
pause
goto SECURITY_TOOLS

:CLEAN_DATABASE
echo.
echo 🧹 Database cleanup options:
echo [1] Remove expired trial codes
echo [2] Remove used codes
echo [3] Clear entire database
echo [4] Cancel
echo.
set /p clean_choice="Select option (1-4): "
if "%clean_choice%"=="4" goto SECURITY_TOOLS
echo.
echo ⚠️ Database cleanup functionality would be implemented here.
echo.
pause
goto SECURITY_TOOLS

:SECURITY_STATS
echo.
echo 📊 Security Statistics:
node -e "
const fs = require('fs');
try {
  const codes = JSON.parse(fs.readFileSync('./used-codes-enhanced.json', 'utf8'));
  const total = Object.keys(codes).length;
  const used = Object.values(codes).filter(c => c.used).length;
  const available = total - used;
  const lifetime = Object.values(codes).filter(c => c.type === 'LIFETIME').length;
  const trial = Object.values(codes).filter(c => c.type === 'TRIAL').length;

  console.log('📈 Total Codes: ' + total);
  console.log('✅ Available: ' + available);
  console.log('❌ Used: ' + used);
  console.log('🔒 Lifetime: ' + lifetime);
  console.log('🧪 Trial: ' + trial);
} catch (e) {
  console.log('📭 No database found');
}
"
echo.
pause
goto SECURITY_TOOLS

:HELP
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              📖 Help & Documentation                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 ENHANCED SECURITY FEATURES:
echo.
echo 1. 🔒 BASIC SECURITY:
echo    • Machine ID binding
echo    • AES-256 encryption
echo    • One-time use validation
echo.
echo 2. 🛡️ ENHANCED SECURITY:
echo    • Hardware fingerprinting
echo    • Network validation
echo    • IP restrictions
echo    • Multi-device management
echo.
echo 3. 🔐 MAXIMUM SECURITY:
echo    • All enhanced features
echo    • Behavioral analysis
echo    • Anti-tampering detection
echo    • Continuous monitoring
echo    • Geolocation restrictions
echo.
echo 📞 SUPPORT:
echo    Phone: +213 551 93 05 89
echo    Email: <EMAIL>
echo    Website: www.icodedz.com
echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo 👋 Thank you for using Enhanced Secure Code Generator v2.0!
echo.
echo 🔐 Your activation codes are secured with multiple layers of protection.
echo 📞 For support: +213 551 93 05 89
echo.
pause
exit

:ERROR
echo.
echo ❌ An error occurred. Please check your Node.js installation.
echo.
pause
goto MAIN_MENU
